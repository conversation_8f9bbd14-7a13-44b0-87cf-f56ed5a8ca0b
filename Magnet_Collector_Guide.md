# 磁力链接搜集器使用指南

## 📋 概述

磁力链接搜集器是一个基于现有JAV采集系统的独立工具，专门用于搜集最佳磁力链接并保存为TXT格式。它依托现有的`make scraper`系统，调用JavBus(主) + U3C3(辅助)数据源，使用与scraper相同的磁力选择逻辑。

## 🎯 核心特性

- **数据源优先级**: JavBus(主) → U3C3(辅助)
- **磁力选择策略**: 与`make scraper`保持一致
- **筛选条件**: 4K优先，最大文件，无中文文件名
- **输出格式**: TXT文件，格式化显示
- **批量处理**: 支持多种批量搜集模式

## 🚀 快速开始

### 1. 编译程序
```bash
cd /www/wwwroot/JAVAPI.COM
make build-collector
```

### 2. 确保外部服务运行
```bash
# 确保JavBus API服务运行在端口3001
# 确保U3C3服务运行在端口3002
```

### 3. 运行程序
```bash
./bin/magnet_collector
```

## 📖 功能说明

### 🎯 主要功能

1. **搜集单个影片磁力链接** - 输入单个影片代码
2. **批量搜集影片磁力链接** - 输入多个影片代码
3. **从文件读取影片代码批量搜集** - 从文本文件读取影片列表
4. **搜集系列影片磁力链接** - 按系列代码和编号范围搜集

### 🔍 磁力选择逻辑

基于`make scraper`的磁力选择器逻辑：

#### 评分权重
- **文件大小**: 40%权重
- **视频质量**: 30%权重  
- **4K/HD加分**: 20%权重
- **文件名规范性**: 10%权重

#### 质量优先级
1. **4K/UHD/2160p**: 最高优先级
2. **1080p**: 高优先级
3. **720p**: 中等优先级
4. **480p**: 低优先级

#### 文件大小评分
- **4K文件**: 20GB+ (100分) → 15GB+ (90分) → 10GB+ (80分)
- **普通文件**: 8GB+ (100分) → 6GB+ (95分) → 4GB+ (90分)

#### 文件名筛选
- **优先**: @符号格式文件名 (如 hhd800.com@SONE-003.mp4)
- **过滤**: 包含中文字符的文件名
- **规范**: 英文数字下划线格式

## 📝 使用示例

### 示例1: 搜集单个影片
```
选择操作: 1
请输入影片代码: SONE-003
```

### 示例2: 批量搜集
```
选择操作: 2
请输入影片代码列表: SONE-001 SONE-002 SONE-003 DASS-001
```

### 示例3: 从文件搜集
```
选择操作: 3
请输入包含影片代码的文件路径: movie_list.txt
```

文件格式示例 (movie_list.txt):
```
SONE-001
SONE-002
SONE-003
# 这是注释，会被忽略
DASS-001
DASS-002
```

### 示例4: 系列搜集
```
选择操作: 4
请输入系列代码: SONE
请输入起始编号: 1
请输入结束编号: 10
```

## 📁 输出格式

### TXT文件格式
```
# 磁力链接搜集结果
# 生成时间: 2024-01-15 14:30:25
# 总数量: 3
# 格式: [4K] 影片代码 标题 文件大小 磁力链接
#================================================================================

[4K] SONE-003 4K機材撮影 驚異のKcupくびれボディを余すことなく堪能する神楽ももか本気（マジ）イキ！初体験3本番 24.51GB magnet:?xt=urn:btih:47758da8910cd88abe83d8d728b4628322900c59&tr=http%3A%2F%2Fsukebei.tracker.wf%3A8888%2Fannounce
[HD] SONE-001 新人NO.1STYLE 神楽ももかAVデビュー 8.45GB magnet:?xt=urn:btih:12345678901234567890123456789012345678&tr=http%3A%2F%2Fsukebei.tracker.wf%3A8888%2Fannounce
```

### 文件命名规则
- **单个影片**: `{影片代码}.txt` (如: SONE-003.txt)
- **批量搜集**: `batch_magnets_{时间戳}.txt`
- **文件搜集**: `file_magnets_{原文件名}_{时间戳}.txt`
- **系列搜集**: `series_{系列代码}_{起始}-{结束}_{时间戳}.txt`

## 📊 输出目录

所有TXT文件保存在 `./magnet_collections/` 目录下。

## ⚙️ 配置说明

### 外部服务配置
- **JavBus API**: `http://localhost:3001`
- **U3C3 API**: `http://localhost:3002`

### 请求间隔
为避免API限制，批量搜集时每个请求间隔500毫秒。

## 🔧 故障排除

### 常见问题

1. **无法连接API服务**
   - 检查JavBus API服务是否运行在端口3001
   - 检查U3C3服务是否运行在端口3002

2. **未找到磁力链接**
   - 影片代码可能不存在
   - 该影片可能没有磁力链接

3. **搜集速度慢**
   - 正常现象，为避免API限制设置了请求间隔
   - 可以分批次搜集大量影片

## 📈 性能建议

- **小批量**: 一次搜集10-50个影片
- **大批量**: 分多次搜集，避免长时间运行
- **系列搜集**: 建议单次不超过100个编号

## 🔄 与现有系统的关系

磁力搜集器是现有JAV采集系统的补充工具：
- **独立运行**: 不影响现有的`make scraper`和`make server`
- **共享逻辑**: 使用相同的磁力选择算法
- **数据源一致**: 调用相同的外部API服务
- **格式兼容**: 输出的磁力链接可直接用于aria2下载

## 🎉 总结

磁力链接搜集器提供了一个简单高效的方式来搜集和整理最佳磁力链接，特别适合：
- 批量获取特定系列的磁力链接
- 为其他下载工具准备磁力链接列表
- 建立个人的磁力链接数据库
- 研究和分析磁力链接质量分布
