#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
M3U8精确时长分析和数据库更新脚本
通过分析本地M3U8文件中的片段时长来计算精确的视频时长
"""

import os
import re
import sys
import glob
import psycopg2
from decimal import Decimal, ROUND_HALF_UP
import argparse
from datetime import datetime

class M3U8DurationAnalyzer:
    def __init__(self, db_config):
        self.db_config = db_config
        self.conn = None

    def format_seconds_to_hms(self, total_seconds):
        """将秒数格式化为HH:MM:SS格式"""
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = total_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}"

    def format_minutes_to_hms(self, total_minutes):
        """将分钟数格式化为HH:MM:SS格式"""
        return self.format_seconds_to_hms(total_minutes * 60)
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def parse_m3u8_duration(self, m3u8_path):
        """解析M3U8文件，计算精确时长"""
        try:
            with open(m3u8_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找所有 #EXTINF: 行
            extinf_pattern = r'#EXTINF:([\d.]+),'
            durations = re.findall(extinf_pattern, content)
            
            if not durations:
                print(f"⚠️ 未找到时长信息: {m3u8_path}")
                return None
            
            # 计算总时长（秒）
            total_seconds = sum(float(d) for d in durations)
            total_minutes = total_seconds / 60

            print(f"📊 M3U8分析结果:")
            print(f"   文件: {os.path.basename(m3u8_path)}")
            print(f"   片段数: {len(durations)}")
            print(f"   总时长: {total_seconds:.2f}秒 = {self.format_seconds_to_hms(total_seconds)}")
            print(f"   分钟数: {total_minutes:.2f} 分钟")
            
            return {
                'total_seconds': total_seconds,
                'total_minutes': total_minutes,
                'segment_count': len(durations),
                'durations': durations
            }
            
        except Exception as e:
            print(f"❌ 解析M3U8失败: {e}")
            return None
    
    def extract_movie_code_from_path(self, m3u8_path):
        """从M3U8文件路径提取影片代码"""
        # 从文件名提取影片代码
        filename = os.path.basename(m3u8_path)

        # 常见的影片代码模式
        patterns = [
            r'playlist_(\w+-\d+)_',  # playlist_MKMP-655_xxx.m3u8
            r'(\w+-\d+)\.m3u8',      # MKMP-655.m3u8
            r'(\w+-\d+)_',           # MKMP-655_xxx.m3u8
        ]

        for pattern in patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                return match.group(1).upper()

        # 如果没有匹配，尝试从目录名提取
        dir_name = os.path.basename(os.path.dirname(m3u8_path))
        for pattern in patterns:
            match = re.search(pattern, dir_name, re.IGNORECASE)
            if match:
                return match.group(1).upper()

        # 🔧 新增：通过数据库反向查找
        return self.find_movie_code_by_playlist_url(m3u8_path)

    def find_movie_code_by_playlist_url(self, m3u8_path):
        """通过TikTok播放列表URL反向查找影片代码"""
        try:
            # 构建相对路径
            relative_path = m3u8_path.replace('/www/wwwroot/JAVAPI.COM', '')
            if not relative_path.startswith('/'):
                relative_path = '/' + relative_path

            cur = self.conn.cursor()

            # 🔧 修复：先查询download_tasks表
            cur.execute("""
                SELECT task_name
                FROM download_tasks
                WHERE tiktok_playlist_url = %s
            """, (relative_path,))

            result = cur.fetchone()
            if result:
                # 从task_name提取影片代码
                task_name = result[0]
                movie_code = self.extract_movie_code_from_filename(task_name)
                if movie_code:
                    print(f"🔍 通过download_tasks找到影片代码: {movie_code}")
                    cur.close()
                    return movie_code

            # 如果download_tasks没找到，再查jav_movies表
            cur.execute("""
                SELECT code
                FROM jav_movies
                WHERE tiktok_playlist_url = %s
            """, (relative_path,))

            result = cur.fetchone()
            cur.close()

            if result:
                print(f"🔍 通过jav_movies找到影片代码: {result[0]}")
                return result[0]

            return None

        except Exception as e:
            print(f"❌ 反向查找失败: {e}")
            return None

    def extract_movie_code_from_filename(self, filename):
        """从文件名提取影片代码"""
        import re

        # 常见的影片代码模式
        patterns = [
            r'(\w+-\d+)',           # MKMP-655
            r'(\d+\w+-\d+)',        # 259LUXU-1844
            r'(\w+\d+-\d+)',        # SONE789-123
        ]

        for pattern in patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                return match.group(1).upper()

        return None
    
    def get_current_duration(self, movie_code):
        """获取数据库中当前的时长"""
        try:
            cur = self.conn.cursor()

            # 🔧 优先查询jav_movies表获取时长
            cur.execute("""
                SELECT duration, updated_at
                FROM jav_movies
                WHERE UPPER(TRIM(code)) = UPPER(TRIM(%s))
            """, (movie_code,))

            result = cur.fetchone()

            # 如果没找到，尝试去掉数字前缀再查找
            if not result and movie_code and movie_code[0].isdigit():
                import re
                match = re.search(r'^\d+(.+)', movie_code)
                if match:
                    clean_code = match.group(1)
                    print(f"🔄 尝试去前缀查找: {movie_code} -> {clean_code}")

                    cur.execute("""
                        SELECT duration, updated_at
                        FROM jav_movies
                        WHERE UPPER(TRIM(code)) = UPPER(TRIM(%s))
                    """, (clean_code,))

                    result = cur.fetchone()
                    if result:
                        print(f"✅ 找到映射: {movie_code} -> {clean_code}")

            cur.close()

            if result:
                return {
                    'duration': float(result[0]) if result[0] else None,
                    'updated_at': result[1]
                }
            return None

        except Exception as e:
            print(f"❌ 查询数据库失败: {e}")
            return None
    
    def update_duration(self, movie_code, precise_minutes, precise_seconds, dry_run=True):
        """更新数据库中的精确时长（三种格式）"""
        try:
            cur = self.conn.cursor()

            # 计算三种格式
            precise_hms = self.format_seconds_to_hms(precise_seconds)

            if dry_run:
                print(f"🔍 [DRY RUN] 将要更新 {movie_code}:")
                print(f"   分钟: {precise_minutes:.2f}")
                print(f"   秒数: {precise_seconds:.2f}")
                print(f"   格式: {precise_hms}")
                return True

            # 使用DECIMAL类型确保精度
            precise_minutes_decimal = Decimal(str(precise_minutes)).quantize(
                Decimal('0.01'), rounding=ROUND_HALF_UP
            )
            precise_seconds_decimal = Decimal(str(precise_seconds)).quantize(
                Decimal('0.001'), rounding=ROUND_HALF_UP
            )

            # 🔧 同时更新三个时长字段
            cur.execute("""
                UPDATE jav_movies
                SET duration = %s,
                    duration_seconds = %s,
                    duration_formatted = %s,
                    updated_at = NOW()
                WHERE UPPER(TRIM(code)) = UPPER(TRIM(%s))
            """, (precise_minutes_decimal, precise_seconds_decimal, precise_hms, movie_code))

            # 如果没有更新到记录，尝试去掉数字前缀
            if cur.rowcount == 0 and movie_code and movie_code[0].isdigit():
                import re
                match = re.search(r'^\d+(.+)', movie_code)
                if match:
                    clean_code = match.group(1)
                    print(f"🔄 尝试去前缀更新: {movie_code} -> {clean_code}")

                    cur.execute("""
                        UPDATE jav_movies
                        SET duration = %s,
                            duration_seconds = %s,
                            duration_formatted = %s,
                            updated_at = NOW()
                        WHERE UPPER(TRIM(code)) = UPPER(TRIM(%s))
                    """, (precise_minutes_decimal, precise_seconds_decimal, precise_hms, clean_code))

            if cur.rowcount > 0:
                self.conn.commit()
                updated_code = movie_code
                if cur.rowcount > 0:
                    print(f"✅ 更新成功: {updated_code}")
                    print(f"   分钟: {precise_minutes_decimal}")
                    print(f"   秒数: {precise_seconds_decimal}")
                    print(f"   格式: {precise_hms}")
                return True
            else:
                print(f"⚠️ 未找到影片记录: {movie_code}")
                return False

        except Exception as e:
            print(f"❌ 更新数据库失败: {e}")
            self.conn.rollback()
            return False
        finally:
            cur.close()
    
    def analyze_single_m3u8(self, m3u8_path, dry_run=True):
        """分析单个M3U8文件并更新数据库"""
        print(f"\n🔍 分析文件: {m3u8_path}")
        
        # 解析M3U8时长
        duration_info = self.parse_m3u8_duration(m3u8_path)
        if not duration_info:
            return False
        
        # 提取影片代码
        movie_code = self.extract_movie_code_from_path(m3u8_path)
        if not movie_code:
            print(f"❌ 无法提取影片代码")
            return False
        
        print(f"📺 影片代码: {movie_code}")
        
        # 获取当前数据库时长
        current_info = self.get_current_duration(movie_code)
        if not current_info:
            print(f"❌ 数据库中未找到影片: {movie_code}")
            return False
        
        current_duration = current_info['duration']
        precise_minutes = duration_info['total_minutes']
        precise_seconds = duration_info['total_seconds']

        print(f"📊 时长对比:")
        if current_duration:
            current_hms = self.format_minutes_to_hms(current_duration)
            print(f"   数据库当前: {current_duration} 分钟 = {current_hms}")
        else:
            print(f"   数据库当前: NULL")

        precise_hms = self.format_seconds_to_hms(precise_seconds)
        print(f"   M3U8精确: {precise_seconds:.2f}秒 = {precise_hms}")
        print(f"   M3U8分钟: {precise_minutes:.2f} 分钟")

        if current_duration:
            diff_minutes = abs(precise_minutes - current_duration)
            diff_seconds = diff_minutes * 60
            print(f"   时长差异: {diff_seconds:.1f}秒 = {diff_minutes:.3f}分钟")
            
            if diff_seconds < 1:  # 差异小于1秒
                print(f"✅ 时长已经足够精确，无需更新")
                return True
        
        # 更新数据库
        return self.update_duration(movie_code, precise_minutes, precise_seconds, dry_run)
    
    def find_all_m3u8_files(self, base_dir="/www/wwwroot/JAVAPI.COM", active_only=True):
        """查找所有M3U8文件"""
        m3u8_files = []

        if active_only:
            # 只查找playlists目录下的活跃文件
            playlist_pattern = os.path.join(base_dir, "playlists", "*.m3u8")
            m3u8_files.extend(glob.glob(playlist_pattern))
            print(f"📋 找到 {len(m3u8_files)} 个活跃M3U8文件 (仅playlists目录)")
        else:
            # 查找所有M3U8文件（包括备份）
            playlist_pattern = os.path.join(base_dir, "playlists", "*.m3u8")
            m3u8_files.extend(glob.glob(playlist_pattern))

            # 查找其他可能的位置
            other_patterns = [
                os.path.join(base_dir, "**", "*.m3u8"),
            ]

            for pattern in other_patterns:
                m3u8_files.extend(glob.glob(pattern, recursive=True))

            # 去重并排序
            m3u8_files = sorted(list(set(m3u8_files)))
            print(f"📋 找到 {len(m3u8_files)} 个M3U8文件 (包括备份)")

        return sorted(m3u8_files)
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            print("📝 数据库连接已关闭")

def main():
    parser = argparse.ArgumentParser(description='M3U8精确时长分析和更新工具')
    parser.add_argument('--file', '-f', help='指定单个M3U8文件进行测试')
    parser.add_argument('--batch', '-b', action='store_true', help='批量处理所有M3U8文件')
    parser.add_argument('--dry-run', action='store_true', default=True, help='只分析不更新（默认）')
    parser.add_argument('--execute', action='store_true', help='实际执行更新操作')
    parser.add_argument('--limit', '-l', type=int, default=10, help='批量处理时的限制数量（默认10）')
    
    args = parser.parse_args()
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'database': 'magnet_downloader',
        'user': 'postgres',
        'password': 'postgres123'
    }
    
    # 创建分析器
    analyzer = M3U8DurationAnalyzer(db_config)
    
    if not analyzer.connect_db():
        return 1
    
    try:
        dry_run = not args.execute
        
        if dry_run:
            print("🔍 [DRY RUN 模式] 只分析不更新，使用 --execute 参数实际执行更新")
        else:
            print("⚡ [执行模式] 将实际更新数据库")
        
        if args.file:
            # 单文件测试
            if not os.path.exists(args.file):
                print(f"❌ 文件不存在: {args.file}")
                return 1
            
            success = analyzer.analyze_single_m3u8(args.file, dry_run)
            return 0 if success else 1
            
        elif args.batch:
            # 批量处理
            m3u8_files = analyzer.find_all_m3u8_files()
            
            if not m3u8_files:
                print("❌ 未找到M3U8文件")
                return 1
            
            print(f"📊 准备处理 {min(len(m3u8_files), args.limit)} 个文件")
            
            success_count = 0
            for i, m3u8_file in enumerate(m3u8_files[:args.limit], 1):
                print(f"\n{'='*60}")
                print(f"📋 处理进度: {i}/{min(len(m3u8_files), args.limit)}")
                
                if analyzer.analyze_single_m3u8(m3u8_file, dry_run):
                    success_count += 1
            
            print(f"\n📊 批量处理完成:")
            print(f"   成功: {success_count}/{min(len(m3u8_files), args.limit)}")
            print(f"   失败: {min(len(m3u8_files), args.limit) - success_count}")
            
        else:
            print("❌ 请指定 --file 或 --batch 参数")
            parser.print_help()
            return 1
            
    finally:
        analyzer.close()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
