#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理孤儿M3U8文件脚本
删除在数据库中没有对应记录的M3U8文件
"""

import os
import glob
import psycopg2
import argparse
from datetime import datetime

def get_orphan_m3u8_files(base_dir="/www/wwwroot/JAVAPI.COM"):
    """查找孤儿M3U8文件"""
    try:
        # 连接数据库
        conn = psycopg2.connect(
            host='localhost',
            database='magnet_downloader',
            user='postgres',
            password='postgres123'
        )
        cur = conn.cursor()
        
        # 获取所有M3U8文件
        playlist_pattern = os.path.join(base_dir, "playlists", "*.m3u8")
        all_m3u8_files = glob.glob(playlist_pattern)
        
        print(f"📋 找到 {len(all_m3u8_files)} 个M3U8文件")
        
        # 获取数据库中所有的TikTok URL
        cur.execute("""
            SELECT tiktok_playlist_url 
            FROM jav_movies 
            WHERE tiktok_playlist_url IS NOT NULL 
            AND tiktok_playlist_url != ''
        """)
        
        db_urls = set(row[0] for row in cur.fetchall())
        print(f"📊 数据库中有 {len(db_urls)} 个TikTok URL记录")
        
        # 查找孤儿文件
        orphan_files = []
        for m3u8_file in all_m3u8_files:
            # 构建相对路径
            relative_path = m3u8_file.replace(base_dir, '')
            if not relative_path.startswith('/'):
                relative_path = '/' + relative_path
            
            if relative_path not in db_urls:
                orphan_files.append(m3u8_file)
        
        cur.close()
        conn.close()
        
        return orphan_files
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        return []

def clean_orphan_files(orphan_files, dry_run=True):
    """清理孤儿文件"""
    if not orphan_files:
        print("✅ 没有发现孤儿文件")
        return
    
    print(f"\n🗑️  发现 {len(orphan_files)} 个孤儿文件:")
    
    for i, file_path in enumerate(orphan_files, 1):
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path) / (1024*1024)  # MB
        
        print(f"  {i:2d}. {file_name} ({file_size:.1f} MB)")
        
        if not dry_run:
            try:
                os.remove(file_path)
                print(f"     ✅ 已删除")
            except Exception as e:
                print(f"     ❌ 删除失败: {e}")
    
    if dry_run:
        print(f"\n🔍 [DRY RUN] 以上 {len(orphan_files)} 个文件将被删除")
        print("使用 --execute 参数实际执行删除")
    else:
        print(f"\n✅ 清理完成，删除了 {len(orphan_files)} 个孤儿文件")

def main():
    parser = argparse.ArgumentParser(description='清理孤儿M3U8文件')
    parser.add_argument('--dry-run', action='store_true', default=True, help='只分析不删除（默认）')
    parser.add_argument('--execute', action='store_true', help='实际执行删除操作')
    
    args = parser.parse_args()
    
    dry_run = not args.execute
    
    if dry_run:
        print("🔍 [DRY RUN 模式] 只分析不删除，使用 --execute 参数实际执行删除")
    else:
        print("⚡ [执行模式] 将实际删除孤儿文件")
        print("⚠️  警告: 此操作不可逆，请确认要删除这些文件！")
        
        confirm = input("确认删除孤儿文件? (yes/no): ")
        if confirm.lower() != 'yes':
            print("❌ 操作已取消")
            return 1
    
    # 查找孤儿文件
    orphan_files = get_orphan_m3u8_files()
    
    # 清理孤儿文件
    clean_orphan_files(orphan_files, dry_run)
    
    return 0

if __name__ == "__main__":
    exit(main())
