# 🧲 磁力链接搜集器

## 概述

磁力链接搜集器是基于现有JAV采集系统的独立工具，专门用于搜集最佳磁力链接并保存为TXT格式。

## 核心特性

✅ **数据源**: JavBus(主) + U3C3(辅助)  
✅ **选择策略**: 与`make scraper`保持一致  
✅ **筛选条件**: 4K优先，最大文件，无中文文件名  
✅ **输出格式**: 标准化TXT格式  
✅ **批量处理**: 支持多种批量搜集模式  

## 快速开始

### 1. 编译
```bash
make build-collector
```

### 2. 运行
```bash
./bin/magnet_collector
```

## 功能菜单

```
📋 选择操作:
1. 搜集单个影片磁力链接
2. 批量搜集影片磁力链接  
3. 从文件读取影片代码批量搜集
4. 搜集系列影片磁力链接
5. 退出
```

## 输出示例

```
[4K] SONE-003 4K機材撮影 驚異のKcupくびれボディを余すことなく堪能する神楽ももか本気（マジ）イキ！初体験3本番 24.51GB magnet:?xt=urn:btih:47758da8910cd88abe83d8d728b4628322900c59&tr=http%3A%2F%2Fsukebei.tracker.wf%3A8888%2Fannounce
```

## 磁力选择逻辑

基于`make scraper`的评分算法：
- **文件大小**: 40%权重 (4K文件20GB+最高分)
- **视频质量**: 30%权重 (4K > 1080p > 720p)  
- **4K/HD加分**: 20%权重
- **文件名规范**: 10%权重 (@符号格式优先)

## 文件输出

- **保存目录**: `./magnet_collections/`
- **命名规则**: 
  - 单个: `{影片代码}.txt`
  - 批量: `batch_magnets_{时间戳}.txt`
  - 系列: `series_{系列}_{范围}_{时间戳}.txt`

## 依赖服务

- JavBus API: `http://localhost:3001`
- U3C3 API: `http://localhost:3002`

## 使用场景

🎯 **批量获取特定系列磁力链接**  
🎯 **为下载工具准备磁力链接列表**  
🎯 **建立个人磁力链接数据库**  
🎯 **研究磁力链接质量分布**  

## 示例文件

项目包含 `example_movie_list.txt` 示例文件，可用于测试批量搜集功能。

---

详细使用说明请参考 `Magnet_Collector_Guide.md`
