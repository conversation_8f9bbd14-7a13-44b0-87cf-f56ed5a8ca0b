# JAV API 项目 Makefile

.PHONY: scraper server build clean help db-backup db-migrate db-verify db-rollback update-hd-flags update-hd-from-javbus

# 默认目标
help:
	@echo "🎬 JAV API 项目命令"
	@echo "===================="
	@echo "📦 主要功能:"
	@echo "make scraper         - 启动完整采集器"
	@echo "make server          - 启动主服务器（同步模式）"
	@echo "make server-async    - 启动异步模式服务器"
	@echo "make build           - 编译所有程序"
	@echo "make build-series    - 编译系列下载器"
	@echo "make build-collector - 编译磁力搜集器"
	@echo "make clean           - 清理编译文件"
	@echo ""
	@echo "🎯 系列下载:"
	@echo "./series_downloader  - 批量下载指定系列影片"
	@echo ""
	@echo "🧲 磁力搜集:"
	@echo "./magnet_collector   - 搜集最佳磁力链接保存为TXT"
	@echo ""
	@echo "🔧 数据管理:"
	@echo "make merge-duplicates - 合并重复磁力链接"
	@echo "make smart-merge     - 智能合并重复磁力链接"
	@echo "make cleanup-orphans - 自动清理孤立文件"
	@echo "make preview-orphans - 预览孤立文件"
	@echo "make check-magnets   - 检查磁力链接状态"
	@echo "make update-hd-flags - 更新现有磁力链接的HD标记"
	@echo "make update-hd-from-javbus - 基于JavBus API更新HD标记"
	@echo "make fix-magnet-hash - 修复磁力链接哈希值"
	@echo "make db-backup - 执行数据库备份"
	@echo "make db-migrate - 执行StreamHG到TikTok CDN迁移"
	@echo "make db-verify - 验证数据库迁移结果"
	@echo "make db-rollback - 回滚数据库迁移"
	@echo ""
	@echo "🔄 同步服务管理:"
	@echo "make sync-urls       - 手动执行播放链接同步"
	@echo "make sync-status     - 查看同步服务状态"
	@echo "make sync-logs       - 查看同步服务日志"
	@echo "make sync-install    - 安装同步服务"
	@echo "make sync-uninstall  - 卸载同步服务"

	@echo ""
	@echo "make help            - 显示帮助信息"

# 启动完整采集器
scraper:
	@echo "🚀 启动JAV完整采集器..."
	@/usr/local/go/bin/go run cmd/complete_javbus_scraper/main.go \
		cmd/complete_javbus_scraper/helpers.go \
		cmd/complete_javbus_scraper/helpers_extended.go \
		cmd/complete_javbus_scraper/magnet_selector.go \
		cmd/complete_javbus_scraper/subtitle_selector.go

# 启动主服务器
server:
	@echo "🖥️  启动主服务器..."
	@/usr/local/go/bin/go run cmd/server/main.go

# 启动异步模式服务器
server-async:
	@echo "🚀 启动异步模式服务器..."
	@ASYNC_MODE=true /usr/local/go/bin/go run cmd/server/main.go

# 编译完整采集器
build-scraper:
	@echo "🔨 编译完整采集器..."
	@/usr/local/go/bin/go build -o bin/scraper \
		cmd/complete_javbus_scraper/main.go \
		cmd/complete_javbus_scraper/helpers.go \
		cmd/complete_javbus_scraper/helpers_extended.go \
		cmd/complete_javbus_scraper/magnet_selector.go \
		cmd/complete_javbus_scraper/subtitle_selector.go

# 编译主服务器
build-server:
	@echo "🔨 编译主服务器..."
	@/usr/local/go/bin/go build -o bin/server cmd/server/main.go

# 编译系列下载器
build-series:
	@echo "🔨 编译系列下载器..."
	@/usr/local/go/bin/go build -o bin/series_downloader cmd/series_downloader/main.go

# 编译磁力搜集器
build-collector:
	@echo "🔨 编译磁力搜集器..."
	@/usr/local/go/bin/go build -o bin/magnet_collector cmd/magnet_collector/main.go

# 编译所有程序
build: build-scraper build-server build-series build-collector
	@echo "✅ 编译完成"

# 运行编译后的采集器
run-scraper: build-scraper
	@echo "🚀 运行编译后的采集器..."
	@./bin/scraper

# 运行编译后的服务器
run-server: build-server
	@echo "🖥️  运行编译后的服务器..."
	@./bin/server

# 清理编译文件
clean:
	@echo "🧹 清理编译文件..."
	@rm -rf bin/
	@echo "✅ 清理完成"

# 创建bin目录
bin:
	@mkdir -p bin

# 合并重复磁力链接
merge-duplicates:
	@echo "🔄 合并重复磁力链接..."
	@cd /www/wwwroot/JAVAPI.COM && /usr/local/go/bin/go run cmd/merge_duplicate_magnets/main.go

# 智能合并重复磁力链接
smart-merge:
	@echo "🧠 智能合并重复磁力链接..."
	@cd /www/wwwroot/JAVAPI.COM && /usr/local/go/bin/go run cmd/smart_merge_magnets/main.go

# 自动清理孤立文件
cleanup-orphans:
	@echo "🧹 自动清理孤立文件..."
	@cd /www/wwwroot/JAVAPI.COM && /usr/local/go/bin/go run cmd/auto_cleanup_orphans/main.go

# 预览孤立文件
preview-orphans:
	@echo "👁️  预览孤立文件..."
	@cd /www/wwwroot/JAVAPI.COM && /usr/local/go/bin/go run cmd/auto_cleanup_orphans/main.go --dry-run

# 检查磁力链接状态
check-magnets:
	@echo "🔍 检查磁力链接状态..."
	@/usr/local/go/bin/go run cmd/check_magnets/main.go

# 修复磁力链接哈希值
fix-magnet-hash:
	@echo "🔧 修复磁力链接哈希值..."
	@/usr/local/go/bin/go run cmd/fix_magnet_hash/main.go

# 数据库备份
db-backup:
	@echo "💾 执行数据库备份..."
	@./scripts/execute_migration.sh backup

# 数据库迁移
db-migrate:
	@echo "🚀 执行StreamHG到TikTok CDN迁移..."
	@./scripts/execute_migration.sh migrate

# 验证迁移结果
db-verify:
	@echo "🔍 验证数据库迁移结果..."
	@./scripts/execute_migration.sh verify
	@echo "🔧 运行Go验证工具..."
	@/usr/local/go/bin/go run cmd/verify_migration/main.go

# 回滚数据库迁移
db-rollback:
	@echo "🔄 回滚数据库迁移..."
	@./scripts/execute_migration.sh rollback

# 更新现有磁力链接的HD标记
update-hd-flags:
	@echo "🔧 更新现有磁力链接的HD标记..."
	@cd /www/wwwroot/JAVAPI.COM && /usr/local/go/bin/go run cmd/update_hd_flags/main.go

# 基于JavBus API更新HD标记
update-hd-from-javbus:
	@echo "🔧 基于JavBus API更新HD标记..."
	@cd /www/wwwroot/JAVAPI.COM && /usr/local/go/bin/go run cmd/update_hd_from_javbus/main.go

# 播放链接同步服务管理
sync-urls:
	@echo "🔄 手动执行播放链接同步..."
	@bash scripts/sync_streaming_urls.sh

sync-status:
	@echo "📊 查看同步服务状态..."
	@systemctl status sync-streaming-urls

sync-logs:
	@echo "📋 查看同步服务日志..."
	@tail -f logs/sync_streaming_urls.log

sync-install:
	@echo "📦 安装同步服务..."
	@bash scripts/install_sync_service.sh install

sync-uninstall:
	@echo "🗑️  卸载同步服务..."
	@bash scripts/install_sync_service.sh uninstall