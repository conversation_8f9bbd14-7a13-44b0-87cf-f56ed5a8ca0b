package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/pkg/database"
)

// MagnetInfo 磁力链接信息
type MagnetInfo struct {
	Code      string  `json:"code"`
	Title     string  `json:"title"`
	MagnetURL string  `json:"magnet_url"`
	FileName  string  `json:"file_name"`
	FileSize  int64   `json:"file_size"`
	SizeGB    float64 `json:"size_gb"`
	Quality   string  `json:"quality"`
	IsHD      bool    `json:"is_hd"`
	Is4K      bool    `json:"is_4k"`
	Score     float64 `json:"score"`
	Source    string  `json:"source"`
}

// JavBusAPIMovie JavBus API影片结构
type JavBusAPIMovie struct {
	ID      string `json:"id"`
	Title   string `json:"title"`
	Magnets []struct {
		Link       string `json:"link"`
		Title      string `json:"title"`
		Size       string `json:"size"`
		NumberSize int64  `json:"numberSize"`
		IsHD       bool   `json:"isHD"`
	} `json:"magnets"`
}

// U3C3APIResponse U3C3 API响应结构
type U3C3APIResponse struct {
	Success bool `json:"success"`
	Data    struct {
		Code    string `json:"code"`
		Title   string `json:"title"`
		Magnets []struct {
			Link     string `json:"link"`
			Title    string `json:"title"`
			Size     string `json:"size"`
			FileSize int64  `json:"file_size"`
			Quality  string `json:"quality"`
		} `json:"magnets"`
	} `json:"data"`
}

// MagnetCollector 磁力搜集器
type MagnetCollector struct {
	javbusURL string
	u3c3URL   string
	outputDir string
}

func main() {
	fmt.Println("🧲 磁力链接搜集器")
	fmt.Println("==========================================")
	fmt.Println("基于现有采集系统，调用JavBus(主) + U3C3(辅助)")
	fmt.Println("磁力选择器与make scraper保持一致")
	fmt.Println("只保留一个最佳磁力链接，4K优先，最大文件，无中文")
	fmt.Println()

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	if err := database.Init(&cfg.Database); err != nil {
		log.Fatalf("Failed to init database: %v", err)
	}
	defer database.Close()

	// 创建磁力搜集器
	collector := &MagnetCollector{
		javbusURL: "http://localhost:3001", // javbus-api服务
		u3c3URL:   "http://localhost:3002", // u3c3服务
		outputDir: "./magnet_collections",
	}

	// 确保输出目录存在
	if err := os.MkdirAll(collector.outputDir, 0755); err != nil {
		log.Fatalf("创建输出目录失败: %v", err)
	}

	// 显示菜单
	for {
		fmt.Println("\n📋 选择操作:")
		fmt.Println("1. 搜集单个影片磁力链接")
		fmt.Println("2. 批量搜集影片磁力链接")
		fmt.Println("3. 从文件读取影片代码批量搜集")
		fmt.Println("4. 搜集系列影片磁力链接")
		fmt.Println("5. 退出")
		fmt.Print("\n请选择 (1-5): ")

		var choice string
		fmt.Scanln(&choice)

		switch choice {
		case "1":
			collector.collectSingleMovie()
		case "2":
			collector.collectBatchMovies()
		case "3":
			collector.collectFromFile()
		case "4":
			collector.collectSeriesMovies()
		case "5":
			fmt.Println("👋 再见!")
			return
		default:
			fmt.Println("❌ 无效选择，请重试")
		}
	}
}

// collectSingleMovie 搜集单个影片
func (c *MagnetCollector) collectSingleMovie() {
	fmt.Print("请输入影片代码: ")
	var code string
	fmt.Scanln(&code)

	if code == "" {
		fmt.Println("❌ 影片代码不能为空")
		return
	}

	code = strings.ToUpper(strings.TrimSpace(code))
	fmt.Printf("\n🔍 开始搜集影片: %s\n", code)

	magnet, err := c.searchBestMagnet(code)
	if err != nil {
		fmt.Printf("❌ 搜集失败: %v\n", err)
		return
	}

	if magnet == nil {
		fmt.Printf("⚠️  未找到合适的磁力链接: %s\n", code)
		return
	}

	// 保存到文件
	filename := fmt.Sprintf("%s.txt", code)
	if err := c.saveMagnetToFile([]*MagnetInfo{magnet}, filename); err != nil {
		fmt.Printf("❌ 保存失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 搜集完成: %s\n", filename)
	c.printMagnetInfo(magnet)
}

// collectBatchMovies 批量搜集影片
func (c *MagnetCollector) collectBatchMovies() {
	fmt.Print("请输入影片代码列表 (用空格分隔): ")
	reader := bufio.NewReader(os.Stdin)
	input, _ := reader.ReadString('\n')
	input = strings.TrimSpace(input)

	if input == "" {
		fmt.Println("❌ 影片代码列表不能为空")
		return
	}

	codes := strings.Fields(input)
	fmt.Printf("\n🔍 开始批量搜集 %d 个影片\n", len(codes))

	var allMagnets []*MagnetInfo
	successCount := 0
	failCount := 0

	for i, code := range codes {
		code = strings.ToUpper(strings.TrimSpace(code))
		fmt.Printf("\n[%d/%d] 搜集: %s", i+1, len(codes), code)

		magnet, err := c.searchBestMagnet(code)
		if err != nil {
			fmt.Printf(" ❌ 失败: %v\n", err)
			failCount++
			continue
		}

		if magnet == nil {
			fmt.Printf(" ⚠️  未找到\n")
			failCount++
			continue
		}

		fmt.Printf(" ✅ 成功 (%.2fGB)\n", magnet.SizeGB)
		allMagnets = append(allMagnets, magnet)
		successCount++

		// 避免请求过快
		time.Sleep(500 * time.Millisecond)
	}

	if len(allMagnets) == 0 {
		fmt.Println("\n❌ 没有搜集到任何磁力链接")
		return
	}

	// 保存到文件
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("batch_magnets_%s.txt", timestamp)
	if err := c.saveMagnetToFile(allMagnets, filename); err != nil {
		fmt.Printf("❌ 保存失败: %v\n", err)
		return
	}

	fmt.Printf("\n🎉 批量搜集完成!\n")
	fmt.Printf("📊 成功: %d, 失败: %d\n", successCount, failCount)
	fmt.Printf("📁 保存文件: %s\n", filename)
}

// collectFromFile 从文件读取影片代码批量搜集
func (c *MagnetCollector) collectFromFile() {
	fmt.Print("请输入包含影片代码的文件路径: ")
	var filePath string
	fmt.Scanln(&filePath)

	if filePath == "" {
		fmt.Println("❌ 文件路径不能为空")
		return
	}

	// 读取文件
	file, err := os.Open(filePath)
	if err != nil {
		fmt.Printf("❌ 打开文件失败: %v\n", err)
		return
	}
	defer file.Close()

	var codes []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" && !strings.HasPrefix(line, "#") {
			codes = append(codes, line)
		}
	}

	if len(codes) == 0 {
		fmt.Println("❌ 文件中没有找到有效的影片代码")
		return
	}

	fmt.Printf("\n🔍 从文件读取到 %d 个影片代码\n", len(codes))

	var allMagnets []*MagnetInfo
	successCount := 0
	failCount := 0

	for i, code := range codes {
		code = strings.ToUpper(strings.TrimSpace(code))
		fmt.Printf("\n[%d/%d] 搜集: %s", i+1, len(codes), code)

		magnet, err := c.searchBestMagnet(code)
		if err != nil {
			fmt.Printf(" ❌ 失败: %v\n", err)
			failCount++
			continue
		}

		if magnet == nil {
			fmt.Printf(" ⚠️  未找到\n")
			failCount++
			continue
		}

		fmt.Printf(" ✅ 成功 (%.2fGB)\n", magnet.SizeGB)
		allMagnets = append(allMagnets, magnet)
		successCount++

		// 避免请求过快
		time.Sleep(500 * time.Millisecond)
	}

	if len(allMagnets) == 0 {
		fmt.Println("\n❌ 没有搜集到任何磁力链接")
		return
	}

	// 保存到文件
	timestamp := time.Now().Format("20060102_150405")
	baseFilename := strings.TrimSuffix(filepath.Base(filePath), filepath.Ext(filePath))
	filename := fmt.Sprintf("file_magnets_%s_%s.txt", baseFilename, timestamp)
	if err := c.saveMagnetToFile(allMagnets, filename); err != nil {
		fmt.Printf("❌ 保存失败: %v\n", err)
		return
	}

	fmt.Printf("\n🎉 文件批量搜集完成!\n")
	fmt.Printf("📊 成功: %d, 失败: %d\n", successCount, failCount)
	fmt.Printf("📁 保存文件: %s\n", filename)
}

// collectSeriesMovies 搜集系列影片
func (c *MagnetCollector) collectSeriesMovies() {
	fmt.Print("请输入系列代码 (如 SONE): ")
	var seriesCode string
	fmt.Scanln(&seriesCode)

	if seriesCode == "" {
		fmt.Println("❌ 系列代码不能为空")
		return
	}

	fmt.Print("请输入起始编号: ")
	var startNum int
	fmt.Scanln(&startNum)

	fmt.Print("请输入结束编号: ")
	var endNum int
	fmt.Scanln(&endNum)

	if startNum <= 0 || endNum <= 0 || startNum > endNum {
		fmt.Println("❌ 编号范围无效")
		return
	}

	seriesCode = strings.ToUpper(strings.TrimSpace(seriesCode))
	fmt.Printf("\n🔍 开始搜集系列: %s-%03d 到 %s-%03d\n", seriesCode, startNum, seriesCode, endNum)

	var allMagnets []*MagnetInfo
	successCount := 0
	failCount := 0

	for i := startNum; i <= endNum; i++ {
		code := fmt.Sprintf("%s-%03d", seriesCode, i)
		fmt.Printf("\n[%d/%d] 搜集: %s", i-startNum+1, endNum-startNum+1, code)

		magnet, err := c.searchBestMagnet(code)
		if err != nil {
			fmt.Printf(" ❌ 失败: %v\n", err)
			failCount++
			continue
		}

		if magnet == nil {
			fmt.Printf(" ⚠️  未找到\n")
			failCount++
			continue
		}

		fmt.Printf(" ✅ 成功 (%.2fGB)\n", magnet.SizeGB)
		allMagnets = append(allMagnets, magnet)
		successCount++

		// 避免请求过快
		time.Sleep(500 * time.Millisecond)
	}

	if len(allMagnets) == 0 {
		fmt.Println("\n❌ 没有搜集到任何磁力链接")
		return
	}

	// 保存到文件
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("series_%s_%03d-%03d_%s.txt", seriesCode, startNum, endNum, timestamp)
	if err := c.saveMagnetToFile(allMagnets, filename); err != nil {
		fmt.Printf("❌ 保存失败: %v\n", err)
		return
	}

	fmt.Printf("\n🎉 系列搜集完成!\n")
	fmt.Printf("📊 成功: %d, 失败: %d\n", successCount, failCount)
	fmt.Printf("📁 保存文件: %s\n", filename)
}

// searchBestMagnet 搜索最佳磁力链接
func (c *MagnetCollector) searchBestMagnet(code string) (*MagnetInfo, error) {
	// 1. 优先从JavBus搜索
	magnet, err := c.searchFromJavBus(code)
	if err == nil && magnet != nil {
		magnet.Source = "javbus"
		return magnet, nil
	}

	// 2. 如果JavBus失败，尝试U3C3
	magnet, err = c.searchFromU3C3(code)
	if err == nil && magnet != nil {
		magnet.Source = "u3c3"
		return magnet, nil
	}

	return nil, fmt.Errorf("所有数据源都未找到影片: %s", code)
}

// searchFromJavBus 从JavBus搜索
func (c *MagnetCollector) searchFromJavBus(code string) (*MagnetInfo, error) {
	url := fmt.Sprintf("%s/api/movies/%s", c.javbusURL, code)

	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("JavBus API请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("JavBus API返回状态码: %d", resp.StatusCode)
	}

	var movie JavBusAPIMovie
	if err := json.NewDecoder(resp.Body).Decode(&movie); err != nil {
		return nil, fmt.Errorf("JavBus API响应解析失败: %v", err)
	}

	if len(movie.Magnets) == 0 {
		return nil, fmt.Errorf("JavBus未找到磁力链接")
	}

	// 转换并选择最佳磁力链接
	var magnets []*MagnetInfo
	for _, m := range movie.Magnets {
		magnet := &MagnetInfo{
			Code:      code,
			Title:     movie.Title,
			MagnetURL: m.Link,
			FileName:  m.Title,
			FileSize:  m.NumberSize,
			SizeGB:    float64(m.NumberSize) / (1024 * 1024 * 1024),
			IsHD:      m.IsHD,
		}

		// 解析质量和4K标识
		magnet.Quality = c.parseQuality(m.Title)
		magnet.Is4K = c.is4K(m.Title)

		// 计算评分
		magnet.Score = c.calculateScore(magnet)

		magnets = append(magnets, magnet)
	}

	return c.selectBestMagnet(magnets), nil
}

// searchFromU3C3 从U3C3搜索
func (c *MagnetCollector) searchFromU3C3(code string) (*MagnetInfo, error) {
	url := fmt.Sprintf("%s/api/movie/%s", c.u3c3URL, code)

	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("U3C3 API请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("U3C3 API返回状态码: %d", resp.StatusCode)
	}

	var response U3C3APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("U3C3 API响应解析失败: %v", err)
	}

	if !response.Success || len(response.Data.Magnets) == 0 {
		return nil, fmt.Errorf("U3C3未找到磁力链接")
	}

	// 转换并选择最佳磁力链接
	var magnets []*MagnetInfo
	for _, m := range response.Data.Magnets {
		magnet := &MagnetInfo{
			Code:      code,
			Title:     response.Data.Title,
			MagnetURL: m.Link,
			FileName:  m.Title,
			FileSize:  m.FileSize,
			SizeGB:    float64(m.FileSize) / (1024 * 1024 * 1024),
			Quality:   m.Quality,
		}

		// 解析4K标识
		magnet.Is4K = c.is4K(m.Title)
		magnet.IsHD = strings.Contains(strings.ToLower(m.Quality), "hd") || magnet.Is4K

		// 计算评分
		magnet.Score = c.calculateScore(magnet)

		magnets = append(magnets, magnet)
	}

	return c.selectBestMagnet(magnets), nil
}

// selectBestMagnet 选择最佳磁力链接（4K大文件优先，无字幕，无破解）
func (c *MagnetCollector) selectBestMagnet(magnets []*MagnetInfo) *MagnetInfo {
	if len(magnets) == 0 {
		return nil
	}

	// 第一步：过滤掉不符合条件的文件
	var filtered []*MagnetInfo
	for _, magnet := range magnets {
		// 过滤条件：
		// 1. 不要中文字幕版本
		if c.hasSubtitleKeywords(magnet.FileName) {
			continue
		}

		// 2. 不要破解版本
		if c.hasUncensoredKeywords(magnet.FileName) {
			continue
		}

		// 3. 不要中文文件名
		if c.hasChineseChars(magnet.FileName) {
			continue
		}

		filtered = append(filtered, magnet)
	}

	// 如果过滤后没有文件，放宽条件（只保留非中文文件名）
	if len(filtered) == 0 {
		for _, magnet := range magnets {
			if !c.hasChineseChars(magnet.FileName) {
				filtered = append(filtered, magnet)
			}
		}
	}

	// 如果还是没有，使用原始列表
	if len(filtered) == 0 {
		filtered = magnets
	}

	// 第二步：按优先级排序
	sort.Slice(filtered, func(i, j int) bool {
		a, b := filtered[i], filtered[j]

		// 1. 4K文件优先
		if a.Is4K != b.Is4K {
			return a.Is4K // 4K优先
		}

		// 2. 文件大小优先（越大越好）
		return a.SizeGB > b.SizeGB
	})

	return filtered[0]
}

// calculateScore 计算磁力链接评分（4K大文件优先）
func (c *MagnetCollector) calculateScore(magnet *MagnetInfo) float64 {
	score := 0.0

	// 1. 4K绝对优先 (50%权重)
	if magnet.Is4K {
		score += 100.0 * 0.5
	} else if magnet.IsHD {
		score += 60.0 * 0.5
	} else {
		score += 30.0 * 0.5
	}

	// 2. 文件大小评分 (40%权重) - 越大越好
	sizeScore := c.calculateSizeScore(magnet)
	score += sizeScore * 0.4

	// 3. 文件名规范性 (10%权重) - @符号格式优先
	nameScore := c.calculateNameScore(magnet)
	score += nameScore * 0.1

	return score
}

// calculateSizeScore 计算文件大小评分（越大越好）
func (c *MagnetCollector) calculateSizeScore(magnet *MagnetInfo) float64 {
	sizeGB := magnet.SizeGB

	// 4K文件：更偏向超大文件
	if magnet.Is4K {
		switch {
		case sizeGB >= 30.0:
			return 100.0 // 超大4K文件最高分
		case sizeGB >= 25.0:
			return 95.0
		case sizeGB >= 20.0:
			return 90.0
		case sizeGB >= 15.0:
			return 85.0
		case sizeGB >= 10.0:
			return 75.0
		case sizeGB >= 8.0:
			return 65.0
		default:
			return 40.0
		}
	}

	// 普通文件：也偏向大文件
	switch {
	case sizeGB >= 15.0:
		return 100.0 // 超大普通文件
	case sizeGB >= 10.0:
		return 95.0
	case sizeGB >= 8.0:
		return 90.0
	case sizeGB >= 6.0:
		return 85.0
	case sizeGB >= 4.0:
		return 75.0
	case sizeGB >= 2.0:
		return 60.0
	case sizeGB >= 1.0:
		return 40.0
	default:
		return 10.0 // 小文件很低分
	}
}

// calculateQualityScore 计算质量评分
func (c *MagnetCollector) calculateQualityScore(magnet *MagnetInfo) float64 {
	switch magnet.Quality {
	case "4K":
		return 100.0
	case "1080p":
		return 80.0
	case "720p":
		return 60.0
	case "480p":
		return 40.0
	default:
		return 50.0
	}
}

// calculateNameScore 计算文件名规范性评分
func (c *MagnetCollector) calculateNameScore(magnet *MagnetInfo) float64 {
	fileName := strings.ToLower(magnet.FileName)

	// @符号优质文件名
	if strings.Contains(fileName, "@") {
		return 100.0
	}

	// 规范的文件名格式
	if regexp.MustCompile(`^[a-z0-9\-_\.]+$`).MatchString(fileName) {
		return 80.0
	}

	// 包含中文字符降分
	if c.hasChineseChars(fileName) {
		return 20.0
	}

	return 60.0
}

// parseQuality 解析视频质量
func (c *MagnetCollector) parseQuality(fileName string) string {
	fileName = strings.ToLower(fileName)

	if regexp.MustCompile(`\b(4k|uhd|2160p)\b`).MatchString(fileName) {
		return "4K"
	}
	if regexp.MustCompile(`\b1080p?\b`).MatchString(fileName) {
		return "1080p"
	}
	if regexp.MustCompile(`\b720p?\b`).MatchString(fileName) {
		return "720p"
	}
	if regexp.MustCompile(`\b480p?\b`).MatchString(fileName) {
		return "480p"
	}

	return "unknown"
}

// is4K 检查是否为4K文件
func (c *MagnetCollector) is4K(fileName string) bool {
	fileName = strings.ToLower(fileName)
	return strings.Contains(fileName, "4k") ||
		strings.Contains(fileName, "uhd") ||
		strings.Contains(fileName, "2160p") ||
		strings.Contains(fileName, "[4k]")
}

// hasChineseChars 检查是否包含中文字符
func (c *MagnetCollector) hasChineseChars(text string) bool {
	for _, r := range text {
		if r >= 0x4e00 && r <= 0x9fff {
			return true
		}
	}
	return false
}

// hasSubtitleKeywords 检查是否包含字幕关键词
func (c *MagnetCollector) hasSubtitleKeywords(text string) bool {
	text = strings.ToLower(text)
	subtitleKeywords := []string{
		"字幕", "中文", "中字", "简体", "繁体", "简中", "繁中",
		"chs", "cht", "chinese", "subtitle", "sub",
		"-c", "_c", "c_gg5", "-ch", "_ch", "ch", "CH",
	}

	for _, keyword := range subtitleKeywords {
		if strings.Contains(text, keyword) {
			return true
		}
	}
	return false
}

// hasUncensoredKeywords 检查是否包含破解关键词
func (c *MagnetCollector) hasUncensoredKeywords(text string) bool {
	text = strings.ToLower(text)
	uncensoredKeywords := []string{
		"无码破解", "破解", "uncensored", "leaked", "leak",
		"无码流出", "流出", "破解版", "无修正",
	}

	for _, keyword := range uncensoredKeywords {
		if strings.Contains(text, keyword) {
			return true
		}
	}
	return false
}

// isCollectionPack 检查是否为合集包
func (c *MagnetCollector) isCollectionPack(text string) bool {
	text = strings.ToLower(text)

	// 检查合集关键词
	collectionKeywords := []string{
		"合集", "collection", "pack", "bundle", "set",
		"new", "nowatermark", "系列", "series",
	}

	for _, keyword := range collectionKeywords {
		if strings.Contains(text, keyword) {
			return true
		}
	}

	// 检查是否包含多个影片代码（如 SIRO-5463 SIRO-5466）
	// 使用正则表达式匹配影片代码模式
	codePattern := regexp.MustCompile(`[A-Z]{2,6}-\d{3,4}`)
	matches := codePattern.FindAllString(strings.ToUpper(text), -1)

	// 如果找到2个或以上不同的影片代码，认为是合集
	if len(matches) >= 2 {
		uniqueCodes := make(map[string]bool)
		for _, match := range matches {
			uniqueCodes[match] = true
		}
		if len(uniqueCodes) >= 2 {
			return true
		}
	}

	return false
}

// saveMagnetToFile 保存磁力链接到TXT文件
func (c *MagnetCollector) saveMagnetToFile(magnets []*MagnetInfo, filename string) error {
	filePath := filepath.Join(c.outputDir, filename)

	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	// 写入文件头
	writer.WriteString("# 磁力链接搜集结果\n")
	writer.WriteString(fmt.Sprintf("# 生成时间: %s\n", time.Now().Format("2006-01-02 15:04:05")))
	writer.WriteString(fmt.Sprintf("# 总数量: %d\n", len(magnets)))
	writer.WriteString("# 格式: [4K] 影片代码 标题 文件大小 磁力链接\n")
	writer.WriteString("#" + strings.Repeat("=", 80) + "\n\n")

	// 写入磁力链接
	for _, magnet := range magnets {
		// 构建质量标识
		qualityTag := ""
		if magnet.Is4K {
			qualityTag = "[4K] "
		} else if magnet.IsHD {
			qualityTag = "[HD] "
		}

		// 格式化输出
		line := fmt.Sprintf("%s%s %s %.2fGB %s\n",
			qualityTag,
			magnet.Code,
			magnet.Title,
			magnet.SizeGB,
			magnet.MagnetURL)

		writer.WriteString(line)
	}

	fmt.Printf("📁 文件保存路径: %s\n", filePath)
	return nil
}

// printMagnetInfo 打印磁力链接信息
func (c *MagnetCollector) printMagnetInfo(magnet *MagnetInfo) {
	fmt.Println("\n📋 磁力链接详情:")
	fmt.Println(strings.Repeat("-", 60))
	fmt.Printf("🎬 影片代码: %s\n", magnet.Code)
	fmt.Printf("📝 标题: %s\n", magnet.Title)
	fmt.Printf("📁 文件名: %s\n", magnet.FileName)
	fmt.Printf("📏 文件大小: %.2f GB\n", magnet.SizeGB)
	fmt.Printf("🎥 质量: %s", magnet.Quality)
	if magnet.Is4K {
		fmt.Printf(" (4K)")
	} else if magnet.IsHD {
		fmt.Printf(" (HD)")
	}
	fmt.Println()
	fmt.Printf("⭐ 评分: %.2f\n", magnet.Score)
	fmt.Printf("🔗 数据源: %s\n", magnet.Source)
	fmt.Printf("🧲 磁力链接: %s\n", magnet.MagnetURL)
	fmt.Println(strings.Repeat("-", 60))
}
